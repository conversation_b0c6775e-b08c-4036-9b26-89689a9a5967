import os
import dgl
import torch
import torch.utils.data as data
import time
import numpy as np
import scipy.sparse as sp
from tqdm import tqdm
from ogb.nodeproppred import Evaluator


class ClusterDataset(data.Dataset):
    '''
    子图补全操作：
    从语义图中提取出由cluster和influential_node组成的子图;
    clusters是由元路径提取得到的局部信息, influential_node是全局信息;
    转换为dgl.DGLGraph对象, 并添加mask属性, 用于后续的训练和推理
    param:
        clusters: list, 每个元素是一个cluster的节点索引列表
        influential_nodes: list, 每个元素是一个influential_node的节点索引列表
        graph: dgl.DGLGraph, 原始图
        add_nodes: bool, 是否添加influential_nodes到graph中
    '''

    def __init__(self, clusters, influential_nodes, graph, add_nodes=False):
        self.clusters = clusters  # 存储图聚类结果，每个聚类包含节点索引列表
        self.influential_nodes = influential_nodes  # 存储全局信息的节点列表
        self.graph = graph  # 存储原始DGL图对象
        self.add_nodes = add_nodes

    def __len__(self):
        return len(self.clusters)
    
    def __getitem__(self, idx):
        mask = torch.tensor([True for i in range(len(self.clusters[idx]))] +   # 为聚类中的节点创建True掩码
                            [False for j in range(len(self.influential_nodes[idx]))])  # 为剩余节点创建False掩码
        subgraph = dgl.node_subgraph(self.graph, self.clusters[idx] + self.influential_nodes[idx])  # 根据节点索引提取子图
        subgraph.ndata["mask"] = mask  # 将掩码信息添加到子图的节点数据中
        return subgraph  # 返回带有掩码的子图

    
class OptimizedClusterDataset(torch.utils.data.Dataset):
    """
    优化的子图补全操作
    主要改进：
    1. 预计算和缓存子图
    2. 延迟加载机制
    3. 内存使用优化
    """

    def __init__(self, clusters, influential_nodes, graph, add_nodes=False, 
                 use_cache=True, cache_dir="./subgraph_cache"):
        self.clusters = clusters  # 存储图聚类结果
        self.graph = graph  # 存储原始图对象
        self.add_nodes = add_nodes 
        self.influential_nodes = influential_nodes  # 存储全局信息的节点列表
        self.use_cache = use_cache  # 是否使用缓存机制
        self.cache_dir = cache_dir  # 缓存目录路径
        
        self._precompute_metadata()  # 调用预计算方法优化性能
        
        # 缓存管理
        if use_cache:
            os.makedirs(cache_dir, exist_ok=True)  # 创建缓存目录
            self.cache_hits = 0  # 初始化缓存命中次数
            self.cache_misses = 0  # 初始化缓存未命中次数

    def _precompute_metadata(self):
        """预计算子图元数据以加速访问"""
        self.cluster_sizes = [len(cluster) for cluster in self.clusters]  # 计算每个聚类的大小
        self.influential_sizes = [len(inf_nodes) for inf_nodes in self.influential_nodes]  # 计算每个影响节点列表的大小
        self.total_sizes = [c + i for c, i in zip(self.cluster_sizes, self.influential_sizes)]  # 计算每个子图的总节点数
        
        # 预计算mask张量
        self.masks = []  # 初始化掩码列表
        for i, (cluster_size, inf_size) in enumerate(zip(self.cluster_sizes, self.influential_sizes)):  # 遍历每个聚类和影响节点对
            mask = torch.cat([  # 连接两个张量创建完整掩码
                torch.ones(cluster_size, dtype=torch.bool),  # 为聚类节点创建True掩码
                torch.zeros(inf_size, dtype=torch.bool)  # 为全局信息节点创建False掩码
            ])
            self.masks.append(mask)  # 将掩码添加到列表中

    def __len__(self):
        return len(self.clusters)

    def __getitem__(self, idx):
        """支持并发安全的磁盘缓存读取/写入，避免部分写入导致的损坏。
        策略：
        - 命中则尝试读取；如读取失败，视为损坏，进入重建流程。
        - 未命中则通过“.lock”文件做互斥，胜者写入临时文件并原子替换。
        - 败者等待锁释放后再读取。
        """
        cache_file = None
        if self.use_cache:
            cache_file = os.path.join(self.cache_dir, f"subgraph_{idx}.pt")
            # 快速路径：文件存在则尝试读取
            if os.path.exists(cache_file):
                try:
                    self.cache_hits += 1
                    return torch.load(cache_file, map_location="cpu")
                except Exception:
                    # 读取失败，视为损坏，进入重建流程
                    pass
            else:
                self.cache_misses += 1

        # 需要构建子图（首次或损坏重建）
        # 并发保护：使用简单的基于文件的锁 + 原子替换写入
        if self.use_cache:
            lock_path = f"{cache_file}.lock"
            tmp_path = f"{cache_file}.tmp"

            def try_acquire_lock():
                try:
                    fd = os.open(lock_path, os.O_CREAT | os.O_EXCL | os.O_WRONLY)
                    return fd
                except FileExistsError:
                    return None

            fd = try_acquire_lock()
            if fd is None:
                # 其他进程在写，等待其完成
                t0 = time.time()
                while True:
                    # 若最终文件出现且可读取，则直接返回
                    if os.path.exists(cache_file):
                        try:
                            return torch.load(cache_file, map_location="cpu")
                        except Exception:
                            # 仍不可读，则继续等待/或锁被释放后由本进程重建
                            pass
                    # 锁释放后跳出等待，由本进程竞争重建/读取
                    if not os.path.exists(lock_path):
                        break
                    if time.time() - t0 > 600:  # 最长等待10分钟，防止死锁
                        break
                    time.sleep(0.1)
                # 再次尝试快速读取（也许对方已写完）
                if os.path.exists(cache_file):
                    try:
                        return torch.load(cache_file, map_location="cpu")
                    except Exception:
                        # 读取失败，尝试获取锁进行重建
                        fd = try_acquire_lock()
                else:
                    fd = try_acquire_lock()
            # 这里要么已经拿到锁，要么use_cache为False
            if fd is not None:
                try:
                    # 构建子图
                    mask = self.masks[idx]
                    all_nodes = np.concatenate([self.clusters[idx], self.influential_nodes[idx]])
                    subgraph = dgl.node_subgraph(self.graph, all_nodes.tolist())
                    subgraph.ndata["mask"] = mask
                    # 先写入临时文件，再原子替换
                    torch.save(subgraph, tmp_path)
                    os.replace(tmp_path, cache_file)  # 原子操作
                    return subgraph
                finally:
                    try:
                        os.close(fd)
                    except Exception:
                        pass
                    try:
                        if os.path.exists(lock_path):
                            os.remove(lock_path)
                    except Exception:
                        pass
            # 未能成功获取锁（极端情况），或等待后仍无结果则退化为直接计算并返回但不缓存
            mask = self.masks[idx]
            all_nodes = np.concatenate([self.clusters[idx], self.influential_nodes[idx]])
            subgraph = dgl.node_subgraph(self.graph, all_nodes.tolist())
            subgraph.ndata["mask"] = mask
            return subgraph

        # 不使用缓存：直接构建并返回
        mask = self.masks[idx]
        all_nodes = np.concatenate([self.clusters[idx], self.influential_nodes[idx]])
        subgraph = dgl.node_subgraph(self.graph, all_nodes.tolist())
        subgraph.ndata["mask"] = mask
        return subgraph

    def get_cache_stats(self):
        """获取缓存统计信息"""
        if self.use_cache:  # 如果使用缓存
            total_accesses = self.cache_hits + self.cache_misses  # 计算总访问次数
            hit_rate = self.cache_hits / total_accesses if total_accesses > 0 else 0  # 计算缓存命中率
            return {"cache_hits": self.cache_hits, "cache_misses": self.cache_misses,   # 返回缓存统计信息
                    "hit_rate": hit_rate}
        return {"cache_disabled": True}  # 如果未启用缓存，返回禁用信息


def cluster_by_metis(coo_matirx, num_parts):
    '''
    使用metis算法将一个图划分为多个子图
    param:
        coo_matirx: scipy.sparse.coo_matrix, 稀疏矩阵
        num_parts: int, 划分的子图数量
    return:
        clusters: list, 每个元素是一个子图的节点索引列表
    '''
    g = dgl.graph((coo_matirx.row, coo_matirx.col))  # 根据稀疏矩阵的行列索引创建DGL图
    subgraphs = dgl.metis_partition(g, num_parts).values()  # 使用METIS算法将图划分为指定数量的子图
    clusters = []  # 初始化聚类结果列表
    for subgraph in subgraphs:  # 遍历每个子图
        clusters.append(subgraph.ndata[dgl.NID].tolist())  # 提取子图的节点ID并转换为列表
    return clusters  # 返回聚类结果


def cluster_by_random(num_nodes, num_parts, seed=42):
    '''
    随机将节点划分为多个子图（用于消融实验模型III）
    param:
        num_nodes: int, 节点总数
        num_parts: int, 划分的子图数量
        seed: int, 随机种子
    return:
        clusters: list, 每个元素是一个子图的节点索引列表
    '''
    import random  # 导入随机数模块
    random.seed(seed)  # 设置随机种子保证结果可重现

    # 创建节点列表并随机打乱
    nodes = list(range(num_nodes))  # 创建从0到num_nodes-1的节点索引列表
    random.shuffle(nodes)  # 随机打乱节点顺序

    # 计算每个分区的大小
    nodes_per_part = num_nodes // num_parts  # 计算每个分区的基本节点数
    remainder = num_nodes % num_parts  # 计算剩余节点数

    clusters = []  # 初始化聚类结果列表
    start_idx = 0  # 初始化起始索引

    for i in range(num_parts):  # 遍历每个分区
        # 前remainder个分区多分配一个节点
        part_size = nodes_per_part + (1 if i < remainder else 0)  # 计算当前分区大小
        end_idx = start_idx + part_size  # 计算结束索引
        clusters.append(nodes[start_idx:end_idx])  # 将节点片段添加到聚类结果
        start_idx = end_idx  # 更新起始索引

    return clusters  # 返回随机聚类结果


def get_ogb_evaluator(dataset):
    '''
    根据数据集名称获取Open Graph Benchmark的评估器
    param:
        dataset: str, 数据集名称
    return:
        evaluator: function, 评估器
    '''
    evaluator = Evaluator(name=dataset)  # 创建OGB评估器实例
    return lambda preds, labels: evaluator.eval({  # 返回lambda函数用于评估
        "y_true": labels.view(-1, 1),  # 将真实标签重塑为列向量
        "y_pred": preds.view(-1, 1),  # 将预测结果重塑为列向量
    })["acc"]  # 返回准确率评估结果


def get_n_params(model):
    '''
    获取模型参数数量
    param:
        model: torch.nn.Module, 模型
    return:
        pp: int, 模型参数数量
    '''
    pp = 0  # 初始化参数计数器
    for p in list(model.parameters()):  # 遍历模型的所有参数
        nn = 1  # 初始化当前参数的元素数量
        for s in list(p.size()):  # 遍历参数张量的每个维度
            nn = nn * s  # 计算当前参数的总元素数
        pp += nn  # 累加到总参数数量
    return pp  # 返回模型总参数数量


def cluster(node2node, num_parts, nodes, adj, idx):
    '''
    子图划分：
    使用metis算法将一个图划分为多个子图;
    该方法的效果可与通过贪心算法分配节点的效果相媲美，并且用时更少
    param:
        node2node: list, 每个元素是一个子图的节点索引列表
        num_parts: int, 划分的子图数量
        nodes: list, 节点索引列表
        adj: scipy.sparse.coo_matrix, 稀疏矩阵
        idx: int, 指定位置的索引
    return:
        clusters: list, 每个元素是一个子图的节点索引列表
    '''
    t_1 = time.time()  # 记录开始时间
    sub_mat = [[] for i in range(len(nodes) - 1)]  # 初始化子矩阵列表
    for i in range(len(nodes) - 1):  # 遍历行索引
        for j in range(len(nodes) - 1):  # 遍历列索引
            sub_mat[i].append(adj[nodes[i]: nodes[i + 1], nodes[j]: nodes[j + 1]])  # 提取邻接矩阵的子块
    sub_mat[idx][idx] = node2node  # 设置指定位置的子矩阵
    new_adj = sp.bmat(sub_mat, dtype=adj.dtype)  # 将子矩阵块组合成新的稀疏矩阵
    clusters = cluster_by_metis(new_adj.tocoo(), num_parts=num_parts)  # 使用METIS算法进行图聚类
    print("clustering costs {:.4f}s".format(time.time() - t_1))  # 输出聚类耗时
    return clusters  # 返回聚类结果


def global_clusters(adj, clusters, limit, node_degree):
    '''
    将多个子图的节点映射到全局节点
    param:
        adj: scipy.sparse.coo_matrix, 稀疏矩阵
        clusters: list, 每个元素是一个子图的节点索引列表
        limit: int, 每个子图的节点数量
        node_degree: list, 每个节点的度数
    return:
        indices_nodes: list, 每个元素是一个子图的节点索引列表
    '''
    trans_matrix = sp.lil_matrix((adj.shape[0], len(clusters)))  # 创建转换矩阵，行数为节点数，列数为聚类数
    for i in tqdm(range(len(clusters))):  # 遍历每个聚类，显示进度条
        for j in clusters[i]:  # 遍历聚类中的每个节点
            trans_matrix[j, i] = 1  # 在转换矩阵中标记节点属于哪个聚类
    trans_matrix = trans_matrix.tocsr()  # 转换为压缩稀疏行格式以提高效率
    local2global = adj.dot(trans_matrix).toarray()  # 计算邻接矩阵与转换矩阵的乘积
    mask = torch.ones((len(clusters), adj.shape[0]))  # 创建掩码矩阵
    for i in range(mask.shape[0]):  # 遍历每个聚类
        mask[i][clusters[i]] = 0  # 将聚类内部节点的掩码设为0
    local2global = torch.mul(torch.from_numpy(local2global), mask.T)  # 应用掩码到local2global矩阵
    values_nodes, indices_nodes = local2global.topk(limit, dim=0, largest=True, sorted=True)  # 找到每个聚类的top-k节点
    return indices_nodes.T.tolist()  # 返回转置后的节点索引列表


def accuracy(preds, labels):
    '''
    计算准确率
    param:
        preds: torch.Tensor, 预测结果
        labels: torch.Tensor, 真实标签
    return:
        acc: float, 准确率
    '''
    result = torch.tensor([preds[i] == labels[i] for i in range(preds.shape[0])])  # 逐元素比较预测值和真实标签
    return (torch.sum(result) / preds.shape[0]).item()  # 计算正确预测的比例并转换为Python数值


def distributed_sum(prob):
    '''
    分布式求和
    param:
        prob: torch.Tensor, 概率
    return:
        result: torch.Tensor, 求和结果
    '''
    output_tensors = [prob.clone() for _ in range(torch.distributed.get_world_size())]  # 为每个进程创建张量副本
    torch.distributed.all_gather(output_tensors, prob)  # 收集所有进程的张量
    result= sum(output_tensors)  # 对所有张量求和
    # 截断由SequentialDistributedSampler添加的虚拟元素
    return result  # 返回求和结果
